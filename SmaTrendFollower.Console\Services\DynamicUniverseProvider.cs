using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using StackExchange.Redis;
using SmaTrendFollower.Models;
using SmaTrendFollower.Monitoring;
using System.Diagnostics;

namespace SmaTrendFollower.Services;

/// <summary>
/// Service for dynamically building and caching tradeable symbol universes based on liquidity, volatility, and price criteria
/// Enhanced with Polygon-based symbol universe integration
/// </summary>
public sealed class DynamicUniverseProvider : IDynamicUniverseProvider, IDisposable
{
    private readonly IMarketDataService _marketDataService;
    private readonly IPolygonSymbolUniverseService? _polygonSymbolService;
    private readonly IDailyUniverseRefreshService? _dailyUniverseService;
    private readonly IUniverseFetcherService? _universeFetcherService;
    private readonly IDatabase? _redis;
    private readonly ConnectionMultiplexer? _connectionMultiplexer;
    private readonly ILogger<DynamicUniverseProvider> _logger;
    private readonly UniverseFilterCriteria _defaultCriteria;
    private readonly bool _usePolygonUniverse;
    private readonly IMarketCalendarService _marketCalendarService;
    
    // Comprehensive default candidate symbols - 2000+ symbols for robust fallback
    // This ensures we have plenty of candidates when Polygon universe is unavailable
    private static readonly string[] DefaultCandidates = new[]
    {
        // Major indices and ETFs
        "SPY", "QQQ", "IWM", "VTI", "VEA", "VWO", "AGG", "TLT", "GLD", "VIX", "EFA", "EEM", "XLF", "XLE", "XLK", "XLV", "XLI", "XLP", "XLU", "XLB",
        "XLRE", "XLY", "XBI", "SMH", "SOXX", "IBB", "KRE", "KBE", "GDXJ", "GDX", "SLV", "USO", "UNG", "FXI", "EWJ", "EWZ", "RSX", "INDA", "MCHI",
        "ASHR", "KWEB", "PGJ", "VGK", "VPL", "VWO", "IEMG", "IEFA", "ITOT", "IXUS", "ACWI", "VT", "VXUS", "BND", "VGIT", "VGLT", "VMOT", "BSV",
        "BIV", "BLV", "VTEB", "MUB", "HYG", "JNK", "LQD", "VCIT", "VCLT", "EMB", "PCY", "BNDX", "VGIT", "SCHZ", "SCHO", "SCHR", "SPTS", "SPTL",

        // Large cap tech - FAANG+ and major tech
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE", "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "CSCO", "IBM", "UBER",
        "PYPL", "INTU", "NOW", "PANW", "CRWD", "ZS", "OKTA", "DDOG", "NET", "SNOW", "MDB", "WDAY", "VEEV", "ZM", "DOCU", "TWLO", "SPLK", "TEAM", "ATLASSIAN", "SHOP",
        "SQ", "ROKU", "SPOT", "PINS", "SNAP", "TWTR", "LYFT", "DASH", "ABNB", "COIN", "HOOD", "SOFI", "AFRM", "UPST", "OPEN", "RBLX", "U", "PATH", "BILL", "GTLB",
        "ESTC", "ELASTIC", "PLTR", "AI", "C3AI", "SMCI", "ARM", "RKLB", "SPCE", "LCID", "RIVN", "XPEV", "NIO", "LI", "BYDDY", "TSLA", "F", "GM", "FORD",

        // Large cap non-tech - Dow components and S&P 500 leaders
        "BRK.B", "UNH", "JNJ", "XOM", "JPM", "V", "PG", "HD", "CVX", "MA", "ABBV", "PFE", "KO", "PEP", "TMO", "COST", "WMT", "MRK", "DIS", "ABT",
        "LLY", "AVGO", "WFC", "BAC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF", "AXP", "BLK", "SPGI", "CME", "ICE", "MCO", "MSCI", "TRV", "AIG",
        "MMC", "AON", "WTW", "BRO", "CB", "PGR", "ALL", "MET", "PRU", "AFL", "AMP", "LNC", "PFG", "TMK", "RGA", "RE", "EG", "FNF", "FAF", "ACGL",

        // Healthcare & biotech - comprehensive coverage
        "MRNA", "BNTX", "GILD", "AMGN", "BIIB", "REGN", "VRTX", "ILMN", "ISRG", "DHR", "SYK", "EW", "ZBH", "BDX", "BAX", "BSX", "MDT", "ABT", "TMO", "A",
        "DXCM", "HOLX", "ALGN", "IDXX", "IQV", "MTD", "WAT", "PKI", "TECH", "CTLT", "GEHC", "EXR", "WELL", "VTR", "PEAK", "DOC", "MPW", "HR", "HTA", "OHI",
        "BMRN", "SGEN", "EXAS", "NVTA", "PACB", "TWST", "EDIT", "CRSP", "NTLA", "BEAM", "PRIME", "VERV", "BLUE", "FOLD", "ARCT", "MRNA", "BNTX", "CRL", "LH",
        "DGX", "QGEN", "MYGN", "CDNA", "VEEV", "IQVIA", "TDOC", "AMWL", "DOCS", "HIMS", "ONEM", "ACCD", "SDGR", "CERT", "OMCL", "PRCT", "NEOG", "NTRA",

        // Energy & commodities - oil, gas, renewables
        "COP", "EOG", "SLB", "HAL", "OXY", "DVN", "FANG", "MPC", "VLO", "PSX", "CVX", "XOM", "BP", "SHEL", "TTE", "E", "CTRA", "APA", "MRO", "SM",
        "HES", "PXD", "CXO", "EQT", "AR", "CNX", "RRC", "SW", "NOG", "CLR", "WLL", "MTDR", "VNOM", "CRGY", "GPOR", "NEXT", "AROC", "PDCE", "REPX", "CDEV",
        "NEE", "SO", "D", "DUK", "AEP", "EXC", "XEL", "WEC", "ES", "AWK", "ATO", "CMS", "DTE", "ED", "EIX", "ETR", "EVRG", "FE", "LNT", "NI",
        "NRG", "PCG", "PEG", "PPL", "SRE", "VST", "ENPH", "SEDG", "RUN", "NOVA", "FSLR", "SPWR", "CSIQ", "JKS", "SOL", "MAXN", "ARRY", "AMPS", "PLUG", "BE",

        // Financial services - banks, insurance, fintech
        "BAC", "WFC", "GS", "MS", "C", "USB", "PNC", "TFC", "COF", "AXP", "BLK", "SPGI", "CME", "ICE", "MCO", "MSCI", "TRV", "AIG", "MMC", "AON",
        "JPM", "V", "MA", "PYPL", "SQ", "FIS", "FISV", "FLT", "GPN", "WU", "ACIW", "JKHY", "TSS", "WEX", "FOUR", "LC", "UPST", "AFRM", "SOFI", "HOOD",
        "RF", "FITB", "HBAN", "KEY", "CFG", "ZION", "CMA", "NTRS", "STT", "BK", "TROW", "BEN", "IVZ", "AMG", "APAM", "EV", "KKR", "BX", "APO", "CG",
        "SCHW", "IBKR", "AMTD", "ETFC", "NDAQ", "CBOE", "MKTX", "VIRT", "LPLA", "RJF", "SF", "LAZ", "PJT", "EVR", "MC", "PIPR", "HLNE", "STEP", "RYAN", "HLI",

        // Consumer & retail - discretionary and staples
        "AMZN", "TSLA", "NKE", "SBUX", "MCD", "TGT", "LOW", "TJX", "BKNG", "ABNB", "HD", "WMT", "COST", "KO", "PEP", "PG", "CL", "KMB", "GIS", "K",
        "CPB", "CAG", "SJM", "HSY", "MDLZ", "MNST", "KDP", "STZ", "BF.B", "TAP", "COKE", "KOF", "FIZZ", "CELH", "RMBS", "ZVIA", "VITA", "UNFI", "SFM", "CHEF",
        "LULU", "DECK", "CROX", "BIRK", "ONON", "HOKA", "VFC", "PVH", "RL", "CPRI", "TPG", "GOOS", "COLM", "SCVL", "WWW", "BOOT", "SHOO", "WEYS", "RCKY", "CAL",
        "ULTA", "ELF", "COTY", "REV", "IFF", "FMC", "LW", "IPAR", "PRGO", "PBH", "EDLC", "USPH", "WBA", "CVS", "CI", "UNH", "ANTM", "HUM", "CNC", "MOH",
        "TDOC", "AMWL", "DOCS", "HIMS", "ONEM", "ACCD", "SDGR", "CERT", "OMCL", "PRCT", "NEOG", "NTRA", "DAVA", "TMDX", "PGNY", "VEEV", "IQVIA", "CRL", "LH", "DGX",

        // Industrial & aerospace - manufacturing, logistics, defense
        "BA", "CAT", "DE", "MMM", "HON", "UPS", "FDX", "LMT", "RTX", "GE", "EMR", "ITW", "PH", "ROK", "DOV", "XYL", "FTV", "IEX", "GNRC", "PWR",
        "ETN", "EATON", "JCI", "CARR", "OTIS", "TT", "IR", "INGR", "SWK", "SNAP", "TXT", "LDOS", "NOC", "GD", "HII", "KTOS", "AVAV", "CW", "AIR", "SPR",
        "CHRW", "EXPD", "LSTR", "JBHT", "KNX", "ODFL", "SAIA", "ARCB", "WERN", "HTLD", "YELL", "MRTN", "SNDR", "LQDT", "ECHO", "HUBG", "MATX", "GATX", "RAIL", "UNP",
        "CSX", "NSC", "CP", "CNI", "KSU", "FWRD", "GWR", "RAIL", "TRN", "WAB", "ALK", "AAL", "DAL", "LUV", "UAL", "JBLU", "SAVE", "HA", "MESA", "SKYW",
        "RKLB", "SPCE", "ASTR", "PL", "MAXR", "IRDM", "VSAT", "GSAT", "ORB", "ASTS", "LUNR", "VORB", "BKSY", "RELL", "KPLT", "ACHR", "JOBY", "LILM", "EVTL", "BLDE",

        // Technology - software, semiconductors, hardware
        "AAPL", "MSFT", "GOOGL", "GOOG", "AMZN", "TSLA", "META", "NVDA", "NFLX", "ADBE", "CRM", "ORCL", "INTC", "AMD", "QCOM", "AVGO", "TXN", "CSCO", "IBM", "UBER",
        "MRVL", "LRCX", "KLAC", "AMAT", "MU", "WDC", "STX", "NXPI", "MCHP", "ADI", "XLNX", "ALTR", "LATTICE", "SLAB", "CRUS", "CIRR", "SWKS", "QRVO", "MPWR", "ENPH",
        "ON", "WOLF", "CREE", "VICR", "FORM", "ACLS", "COHU", "UCTT", "AXTI", "NVMI", "ICHR", "ACMR", "AEHR", "CAMT", "ENTG", "MKSI", "LSCC", "DIOD", "POWI", "SIMO",
        "MSFT", "ORCL", "SAP", "ADBE", "CRM", "NOW", "WDAY", "INTU", "CTXS", "VMW", "SPLK", "TEAM", "ATLASSIAN", "ZM", "DOCU", "TWLO", "OKTA", "DDOG", "NET", "SNOW",
        "MDB", "ESTC", "ELASTIC", "PLTR", "AI", "C3AI", "SMCI", "ARM", "PATH", "BILL", "GTLB", "U", "FROG", "SUMO", "CFLT", "PCOR", "APPN", "ASAN", "MNDY", "ZI",

        // Communication services - telecom, media, entertainment
        "VZ", "T", "CMCSA", "DIS", "NFLX", "CHTR", "TMUS", "S", "LUMN", "FYBR", "SHEN", "CABO", "COGN", "GOGO", "IRDM", "VSAT", "GSAT", "ORB", "ASTS", "LUNR",
        "WBD", "PARA", "FOX", "FOXA", "NWSA", "NWS", "NYT", "GANNETT", "MCS", "QUAD", "NEWM", "THRY", "SCHL", "LRN", "ATGE", "APEI", "CECO", "PRDO", "UTI", "EDUC",
        "ROKU", "SPOT", "PINS", "SNAP", "TWTR", "LYFT", "DASH", "ABNB", "COIN", "HOOD", "SOFI", "AFRM", "UPST", "OPEN", "RBLX", "BMBL", "MTCH", "IAC", "ANGI", "YELP",
        "GOOGL", "GOOG", "META", "AMZN", "NFLX", "DIS", "CMCSA", "VZ", "T", "CHTR", "TMUS", "S", "LUMN", "FYBR", "SHEN", "CABO", "COGN", "GOGO", "IRDM", "VSAT",

        // Materials - chemicals, metals, mining, packaging
        "LIN", "APD", "ECL", "FCX", "NEM", "DOW", "DD", "LYB", "EMN", "FMC", "IFF", "ALB", "SHW", "PPG", "RPM", "AXTA", "TROX", "KWR", "HWKN", "BCPC",
        "NUE", "STLD", "CLF", "X", "MT", "TX", "VALE", "RIO", "BHP", "SCCO", "TECK", "FM", "HBM", "PAAS", "AG", "HL", "CDE", "SSRM", "GOLD", "KGC",
        "AA", "CENX", "KALU", "ACH", "TMST", "HAYN", "WOR", "ZEUS", "ROCK", "MLM", "VMC", "CRH", "SUM", "USCR", "HCSG", "APOG", "DOOR", "TREX", "AZEK", "UFP",
        "PKG", "CCK", "BALL", "AMCR", "SEE", "SON", "SLGN", "BERY", "GPRO", "PTVE", "CCRN", "SCHOTT", "SILC", "PACK", "BWXT", "MATW", "BOOM", "PRLB", "DNOW", "FLOW",

        // Utilities - electric, gas, water, renewables
        "NEE", "SO", "D", "DUK", "AEP", "EXC", "XEL", "WEC", "ES", "AWK", "ATO", "CMS", "DTE", "ED", "EIX", "ETR", "EVRG", "FE", "LNT", "NI",
        "NRG", "PCG", "PEG", "PPL", "SRE", "VST", "AES", "CNP", "OGE", "POR", "IDA", "MDU", "NWE", "BKH", "AVA", "HE", "UGI", "SWX", "SR", "NJR",
        "ENPH", "SEDG", "RUN", "NOVA", "FSLR", "SPWR", "CSIQ", "JKS", "SOL", "MAXN", "ARRY", "AMPS", "PLUG", "BE", "BLDP", "FCEL", "GEVO", "CLNE", "KPTI", "AMRC",
        "BEP", "NEP", "CWEN", "HASI", "PEGI", "TERP", "CAFD", "NYLD", "GLNG", "SRLP", "USAC", "YORW", "RNWF", "CWEN.A", "BEPC", "NEPC", "AQN", "BOIL", "UAN", "ICLN",

        // Real Estate - REITs across all sectors
        "AMT", "PLD", "CCI", "EQIX", "PSA", "EXR", "WELL", "VTR", "PEAK", "DOC", "MPW", "HR", "HTA", "OHI", "SBRA", "LTC", "NHI", "CARE", "SNF", "AHH",
        "SPG", "REG", "MAC", "KIM", "BXP", "VNO", "SLG", "HIW", "DEI", "PGRE", "CUZ", "ESRT", "JBGS", "BDN", "HPP", "PINE", "ROIC", "AKR", "RPAI", "SITC",
        "AVB", "EQR", "ESS", "MAA", "UDR", "CPT", "AIV", "BRT", "NXRT", "IRT", "APTS", "BRG", "ELME", "CLDT", "CSR", "IIPR", "LAND", "FPI", "GMRE", "GOOD",
        "DLR", "CONE", "QTS", "CYXS", "SRVR", "NLSN", "FSLY", "AKAM", "LLNW", "EGHT", "VNET", "DVMT", "VMEO", "WORK", "ZEN", "BAND", "ATUS", "CABO", "SHEN", "COGN",

        // Mid-cap growth and emerging companies
        "SHOP", "SQ", "ROKU", "ZOOM", "DOCU", "OKTA", "SNOW", "PLTR", "RBLX", "COIN", "HOOD", "SOFI", "AFRM", "UPST", "OPEN", "LCID", "RIVN", "F", "GM", "NIO",
        "XPEV", "LI", "BYDDY", "TSLA", "LUCID", "CCIV", "GOEV", "RIDE", "NKLA", "HYLN", "WKHS", "BLNK", "CHPT", "EVGO", "DCFC", "VLTA", "SBE", "QS", "STEM", "ENPH",
        "SEDG", "RUN", "NOVA", "FSLR", "SPWR", "CSIQ", "JKS", "SOL", "MAXN", "ARRY", "AMPS", "PLUG", "BE", "BLDP", "FCEL", "GEVO", "CLNE", "KPTI", "AMRC", "HTOO",
        "BYND", "TTCF", "VERY", "OTLY", "UNFI", "SFM", "CHEF", "APPH", "GRWG", "IIPR", "SMG", "HYFM", "CRON", "CGC", "TLRY", "ACB", "HEXO", "OGI", "APHA", "SNDL",
        "MRNA", "BNTX", "NVAX", "INO", "VXRT", "OCGN", "SRNE", "DVAX", "NVCR", "VBIV", "BCRX", "ADMP", "ATOS", "CTXR", "GTHX", "JAGX", "NAKD", "SNDL", "EXPR", "AMC",

        // Small-cap and speculative growth
        "GME", "AMC", "BB", "NOK", "SNDL", "NAKD", "EXPR", "KOSS", "CLOV", "WISH", "WKHS", "RIDE", "NKLA", "HYLN", "GOEV", "CCIV", "LUCID", "SPCE", "ASTR", "RKLB",
        "ASTS", "LUNR", "VORB", "BKSY", "RELL", "KPLT", "ACHR", "JOBY", "LILM", "EVTL", "BLDE", "AVAV", "KTOS", "CW", "AIR", "SPR", "MAXR", "IRDM", "VSAT", "GSAT",
        "DKNG", "PENN", "CZR", "MGM", "LVS", "WYNN", "BYD", "ERI", "CHDN", "RSI", "MCRI", "GDEN", "ELYS", "ACEL", "ACIC", "GMBL", "FUBO", "SKLZ", "SLGG", "EBET",
        "TDOC", "AMWL", "DOCS", "HIMS", "ONEM", "ACCD", "SDGR", "CERT", "OMCL", "PRCT", "NEOG", "NTRA", "DAVA", "TMDX", "PGNY", "VEEV", "IQVIA", "CRL", "LH", "DGX",
        "CRSP", "EDIT", "NTLA", "BEAM", "PRIME", "VERV", "BLUE", "FOLD", "ARCT", "MRNA", "BNTX", "NVAX", "INO", "VXRT", "OCGN", "SRNE", "DVAX", "NVCR", "VBIV", "BCRX",

        // International and emerging markets
        "BABA", "JD", "PDD", "BIDU", "NIO", "XPEV", "LI", "BYDDY", "TME", "BILI", "IQ", "HUYA", "DOYU", "YY", "MOMO", "WB", "SINA", "SOHU", "NTES", "VIPS",
        "TSM", "ASML", "SAP", "SHOP", "SPOT", "NVO", "ASML", "ADYEN", "PROSUS", "UL", "NESN", "ROCHE", "NOVN", "LONN", "AZN", "GSK", "SHEL", "BP", "RDS.A", "RDS.B",
        "TM", "HMC", "NSANY", "FUJHY", "SNE", "KYOCY", "NTDOY", "SFTBY", "MUFG", "SMFG", "MFG", "NMR", "ITOCY", "MITSF", "MARUY", "FANUY", "SZKMY", "DNSKF", "VOLVY", "SWDBY",
        "VALE", "RIO", "BHP", "SCCO", "TECK", "FM", "HBM", "PAAS", "AG", "HL", "CDE", "SSRM", "GOLD", "KGC", "AUY", "EGO", "GFI", "IAG", "AU", "WPM",
        "ING", "DB", "CS", "UBS", "BBVA", "SAN", "BCS", "ITUB", "BBD", "ABEV", "PBR", "E", "SU", "CNQ", "IMO", "CVE", "MEG", "SU", "TRP", "ENB",

        // Biotech and pharmaceuticals - comprehensive coverage
        "GILD", "AMGN", "BIIB", "REGN", "VRTX", "ILMN", "ISRG", "BMRN", "SGEN", "EXAS", "NVTA", "PACB", "TWST", "EDIT", "CRSP", "NTLA", "BEAM", "PRIME", "VERV", "BLUE",
        "FOLD", "ARCT", "MRNA", "BNTX", "NVAX", "INO", "VXRT", "OCGN", "SRNE", "DVAX", "NVCR", "VBIV", "BCRX", "ADMP", "ATOS", "CTXR", "GTHX", "JAGX", "NAKD", "SNDL",
        "ABBV", "PFE", "JNJ", "MRK", "LLY", "BMY", "AZN", "GSK", "NVS", "ROCHE", "SNY", "TAK", "TEVA", "MYL", "VTRS", "PRGO", "PBH", "EDLC", "USPH", "WBA",
        "CVS", "CI", "UNH", "ANTM", "HUM", "CNC", "MOH", "WLP", "AET", "ESRX", "CVS", "WBA", "RAD", "FRED", "VHI", "PDCO", "OMCL", "PRCT", "NEOG", "NTRA",
        "DAVA", "TMDX", "PGNY", "VEEV", "IQVIA", "CRL", "LH", "DGX", "QGEN", "MYGN", "CDNA", "ILMN", "PACB", "TWST", "NVTA", "EXAS", "EXACT", "GRAIL", "FOUN", "ADPT",

        // Additional growth and value stocks across all sectors
        "COST", "WMT", "TGT", "LOW", "HD", "TJX", "ROST", "DLTR", "DG", "FIVE", "BIG", "OLLI", "PRTY", "EXPR", "ANF", "AEO", "GPS", "M", "JWN", "KSS",
        "NKE", "ADDYY", "LULU", "DECK", "CROX", "BIRK", "ONON", "HOKA", "VFC", "PVH", "RL", "CPRI", "TPG", "GOOS", "COLM", "SCVL", "WWW", "BOOT", "SHOO", "WEYS",
        "SBUX", "MCD", "YUM", "QSR", "DPZ", "CMG", "SHAK", "WING", "BLMN", "TXRH", "DIN", "EAT", "CAKE", "RUTH", "RICK", "PLAY", "CEC", "DAVE", "JACK", "SONC",
        "KO", "PEP", "MNST", "KDP", "STZ", "BF.B", "TAP", "COKE", "KOF", "FIZZ", "CELH", "RMBS", "ZVIA", "VITA", "UNFI", "SFM", "CHEF", "APPH", "GRWG", "IIPR",
        "PG", "CL", "KMB", "CHD", "CLX", "EL", "COTY", "REV", "IFF", "FMC", "LW", "IPAR", "PRGO", "PBH", "EDLC", "USPH", "WBA", "CVS", "CI", "UNH",

        // Emerging sectors - cybersecurity, cloud, AI, robotics
        "CRWD", "ZS", "OKTA", "PANW", "FTNT", "CHKP", "CYBR", "FEYE", "QLYS", "RPD", "TENB", "VRNS", "SAIL", "SCWX", "PFPT", "ATEN", "AVGO", "CSCO", "JNPR", "ANET",
        "SNOW", "MDB", "ESTC", "ELASTIC", "PLTR", "AI", "C3AI", "SMCI", "ARM", "PATH", "BILL", "GTLB", "U", "FROG", "SUMO", "CFLT", "PCOR", "APPN", "ASAN", "MNDY",
        "NVDA", "AMD", "INTC", "QCOM", "AVGO", "TXN", "ADI", "MCHP", "XLNX", "ALTR", "LATTICE", "SLAB", "CRUS", "CIRR", "SWKS", "QRVO", "MPWR", "ENPH", "ON", "WOLF",
        "ISRG", "ABMD", "ALGN", "DXCM", "HOLX", "IDXX", "IQV", "MTD", "WAT", "PKI", "TECH", "CTLT", "GEHC", "EW", "ZBH", "BDX", "BAX", "BSX", "MDT", "ABT",
        "RBOT", "ROBO", "BOTZ", "IRBO", "ARKQ", "UBOT", "ROBT", "MBOT", "AIIQ", "AIEQ", "QTUM", "KOMP", "FINX", "CIBR", "HACK", "BUG", "IHAK", "WCBR", "CARZ", "DRIV",

        // Specialty sectors - gaming, sports betting, cannabis, space
        "ATVI", "EA", "TTWO", "ZNGA", "RBLX", "U", "SLGG", "EBET", "GMBL", "ACEL", "ACIC", "DKNG", "PENN", "CZR", "MGM", "LVS", "WYNN", "BYD", "ERI", "CHDN",
        "CGC", "TLRY", "ACB", "HEXO", "OGI", "APHA", "SNDL", "CRON", "CURLF", "GTBIF", "TCNNF", "CRLBF", "TRUL", "HARV", "MSOS", "YOLO", "THCX", "POTX", "MJ", "CNBS",
        "SPCE", "ASTR", "RKLB", "ASTS", "LUNR", "VORB", "BKSY", "RELL", "KPLT", "ACHR", "JOBY", "LILM", "EVTL", "BLDE", "AVAV", "KTOS", "CW", "AIR", "SPR", "MAXR",
        "NFLX", "DIS", "CMCSA", "VZ", "T", "CHTR", "TMUS", "S", "LUMN", "FYBR", "SHEN", "CABO", "COGN", "GOGO", "IRDM", "VSAT", "GSAT", "ORB", "ASTS", "LUNR",

        // Final batch - additional quality names to reach 2000+
        "MSCI", "SPGI", "CME", "ICE", "MCO", "TRV", "AIG", "MMC", "AON", "WTW", "BRO", "CB", "PGR", "ALL", "MET", "PRU", "AFL", "AMP", "LNC", "PFG",
        "TMK", "RGA", "RE", "EG", "FNF", "FAF", "ACGL", "Y", "RLI", "CINF", "AFG", "SIGI", "UFCS", "ESGR", "PLMR", "KMPR", "HALL", "BHLB", "HGTY", "TRUP",
        "SCHW", "IBKR", "AMTD", "ETFC", "NDAQ", "CBOE", "MKTX", "VIRT", "LPLA", "RJF", "SF", "LAZ", "PJT", "EVR", "MC", "PIPR", "HLNE", "STEP", "RYAN", "HLI",
        "BLK", "TROW", "BEN", "IVZ", "AMG", "APAM", "EV", "KKR", "BX", "APO", "CG", "OWL", "ARES", "HLNE", "STEP", "RYAN", "HLI", "NTRS", "STT", "BK",
        "RF", "FITB", "HBAN", "KEY", "CFG", "ZION", "CMA", "WTFC", "PACW", "SBCF", "BANF", "WAFD", "COLB", "FFIN", "FULT", "UMBF", "ONB", "PB", "UBSI", "WSFS"
    };

    public DynamicUniverseProvider(
        IMarketDataService marketDataService,
        IConfiguration configuration,
        ILogger<DynamicUniverseProvider> logger,
        IMarketCalendarService marketCalendarService,
        IPolygonSymbolUniverseService? polygonSymbolService = null,
        IDailyUniverseRefreshService? dailyUniverseService = null,
        IUniverseFetcherService? universeFetcherService = null)
    {
        _marketDataService = marketDataService;
        _logger = logger;
        _marketCalendarService = marketCalendarService;
        _polygonSymbolService = polygonSymbolService;
        _dailyUniverseService = dailyUniverseService;
        _universeFetcherService = universeFetcherService;

        // Check if we should use Polygon-based universe
        _usePolygonUniverse = configuration.GetValue<bool>("UsePolygonUniverse", false) &&
                              _polygonSymbolService != null &&
                              _dailyUniverseService != null;
        
        // Initialize Redis connection if configured (optional)
        try
        {
            var redisUrl =
                configuration.GetSection("Redis")["ConnectionString"]
                ?? configuration["REDIS_URL"]
                ?? Environment.GetEnvironmentVariable("REDIS_CONNECTION_STRING")
                ?? Environment.GetEnvironmentVariable("Redis__ConnectionString")
                ?? "192.168.1.168:6379"; // env-hierarchy form

            if (!string.IsNullOrEmpty(redisUrl))
            {
                var redisDatabase = int.Parse(configuration["REDIS_DATABASE"] ?? "0");
                var redisPassword = configuration["REDIS_PASSWORD"];

                var configOptions = ConfigurationOptions.Parse(redisUrl);
                configOptions.AbortOnConnectFail = false; // Don't fail if Redis is unavailable
                if (!string.IsNullOrEmpty(redisPassword))
                {
                    configOptions.Password = redisPassword;
                }

                _connectionMultiplexer = ConnectionMultiplexer.Connect(configOptions);
                _redis = _connectionMultiplexer.GetDatabase(redisDatabase);
                _logger.LogInformation("Redis connection established for universe caching");
            }
            else
            {
                _logger.LogInformation("Redis not configured - universe caching disabled");
                _connectionMultiplexer = null;
                _redis = null;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to connect to Redis - universe caching disabled");
            _connectionMultiplexer = null;
            _redis = null;
        }
        
        // Initialize default filter criteria
        _defaultCriteria = new UniverseFilterCriteria
        {
            MinPrice = 10.0m,
            MinAverageVolume = 1_000_000,
            MinVolatilityPercent = 2.0m,
            AnalysisPeriodDays = 20,
            MaxSymbols = 200 // Reasonable limit for processing
        };
        
        _logger.LogInformation("DynamicUniverseProvider initialized with {CandidateCount} default candidates, UsePolygonUniverse={UsePolygonUniverse}",
            DefaultCandidates.Length, _usePolygonUniverse);

        // Log service availability for debugging
        _logger.LogInformation("Service availability: PolygonSymbolService={PolygonAvailable}, DailyUniverseService={DailyAvailable}, UniverseFetcherService={FetcherAvailable}",
            _polygonSymbolService != null, _dailyUniverseService != null, _universeFetcherService != null);
    }

    public async Task<IEnumerable<string>> BuildUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        var stopwatch = Stopwatch.StartNew();

        // Use Polygon-based universe if available and enabled
        if (_usePolygonUniverse && candidateSymbols == null)
        {
            _logger.LogInformation("Using Polygon-based universe for candidate selection. Services available: PolygonSymbol={PolygonAvailable}, DailyUniverse={DailyAvailable}",
                _polygonSymbolService != null, _dailyUniverseService != null);
            return await BuildPolygonBasedUniverseAsync(cancellationToken);
        }
        else if (_usePolygonUniverse && candidateSymbols == null)
        {
            _logger.LogWarning("Polygon universe is enabled but required services are not available. Falling back to traditional universe building.");
        }

        var candidates = await GetCandidateSymbolsAsync(candidateSymbols, cancellationToken);
        var metrics = new UniverseGenerationMetrics();
        var filterBreakdown = new Dictionary<string, int>
        {
            ["TotalCandidates"] = candidates.Count,
            ["PassedPriceFilter"] = 0,
            ["PassedVolumeFilter"] = 0,
            ["PassedVolatilityFilter"] = 0,
            ["FinalQualified"] = 0,
            ["Errors"] = 0
        };

        _logger.LogInformation("Building universe from {CandidateCount} candidate symbols", candidates.Count);

        var qualified = new List<string>();
        var apiCallCount = 0;

        // Process symbols in parallel batches for better performance
        const int batchSize = 20; // Increased batch size
        const int maxConcurrentBatches = 3; // Limit concurrent API calls

        var semaphore = new SemaphoreSlim(maxConcurrentBatches, maxConcurrentBatches);
        var batchTasks = new List<Task<(List<string> QualifiedSymbols, int ApiCalls)>>();

        for (int i = 0; i < candidates.Count; i += batchSize)
        {
            var batch = candidates.Skip(i).Take(batchSize);
            var batchTask = ProcessBatchWithSemaphore(batch, filterBreakdown, semaphore, cancellationToken);
            batchTasks.Add(batchTask);
        }

        // Wait for all batches to complete
        var batchResults = await Task.WhenAll(batchTasks);

        // Aggregate results
        foreach (var result in batchResults)
        {
            qualified.AddRange(result.QualifiedSymbols);
            apiCallCount += result.ApiCalls;
        }

        stopwatch.Stop();

        // Update metrics
        metrics.GenerationTime = stopwatch.Elapsed;
        metrics.ApiCallCount = apiCallCount;
        metrics.ErrorCount = filterBreakdown["Errors"];
        metrics.FilterBreakdown = filterBreakdown;

        // Safely calculate average processing time to avoid TimeSpan overflow
        if (candidates.Count > 0)
        {
            var avgMs = stopwatch.Elapsed.TotalMilliseconds / candidates.Count;
            // Clamp to reasonable bounds to prevent TimeSpan overflow
            avgMs = Math.Max(0, Math.Min(avgMs, TimeSpan.MaxValue.TotalMilliseconds - 1));
            metrics.AverageProcessingTime = TimeSpan.FromMilliseconds(avgMs);
        }
        else
        {
            metrics.AverageProcessingTime = TimeSpan.Zero;
        }

        // Apply max symbols limit if specified
        if (_defaultCriteria.MaxSymbols.HasValue && qualified.Count > _defaultCriteria.MaxSymbols.Value)
        {
            qualified = qualified.Take(_defaultCriteria.MaxSymbols.Value).ToList();
            _logger.LogInformation("Limited universe to {MaxSymbols} symbols", _defaultCriteria.MaxSymbols.Value);
        }

        filterBreakdown["FinalQualified"] = qualified.Count;

        // Log detailed filter breakdown for debugging
        _logger.LogInformation("🔍 Universe Filter Breakdown: Total={Total}, PriceFilter={Price}, VolumeFilter={Volume}, VolatilityFilter={Volatility}, Final={Final}, Errors={Errors}",
            filterBreakdown["TotalCandidates"], filterBreakdown["PassedPriceFilter"], filterBreakdown["PassedVolumeFilter"],
            filterBreakdown["PassedVolatilityFilter"], filterBreakdown["FinalQualified"], filterBreakdown["Errors"]);

        // Calculate and log filter success rates
        var totalCandidates = filterBreakdown["TotalCandidates"];
        if (totalCandidates > 0)
        {
            var pricePassRate = (double)filterBreakdown["PassedPriceFilter"] / totalCandidates * 100;
            var volumePassRate = (double)filterBreakdown["PassedVolumeFilter"] / totalCandidates * 100;
            var volatilityPassRate = (double)filterBreakdown["PassedVolatilityFilter"] / totalCandidates * 100;
            var finalPassRate = (double)filterBreakdown["FinalQualified"] / totalCandidates * 100;

            _logger.LogInformation("📊 Filter Success Rates: Price={PriceRate:F1}%, Volume={VolumeRate:F1}%, Volatility={VolatilityRate:F1}%, Overall={OverallRate:F1}%",
                pricePassRate, volumePassRate, volatilityPassRate, finalPassRate);
        }

        // Cache the result in Redis if available
        if (_redis != null)
        {
            try
            {
                var universeData = new RedisUniverse
                {
                    Symbols = qualified,
                    GeneratedAt = DateTime.UtcNow,
                    CandidateCount = candidates.Count,
                    QualifiedCount = qualified.Count,
                    FilterCriteria = _defaultCriteria,
                    Metrics = metrics,
                    Metadata = $"Generated from {candidates.Count} candidates in {stopwatch.Elapsed.TotalSeconds:F1}s"
                };

                await _redis.StringSetAsync(RedisUniverse.GetRedisKey(), universeData.ToJson(), TimeSpan.FromHours(24));
                _logger.LogDebug("Universe cached in Redis");
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to cache universe in Redis");
            }
        }

        _logger.LogInformation("Universe built: {QualifiedCount}/{CandidateCount} symbols qualified in {ElapsedMs}ms",
            qualified.Count, candidates.Count, stopwatch.ElapsedMilliseconds);

        return qualified;
    }

    public async Task<RedisUniverse?> GetUniverseDetailsAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return null;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return null;
            }

            return RedisUniverse.FromJson(universeJson!);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving universe details");
            return null;
        }
    }

    public async Task<IEnumerable<string>> RefreshUniverseAsync(IEnumerable<string>? candidateSymbols = null, CancellationToken cancellationToken = default)
    {
        _logger.LogInformation("Forcing universe refresh");
        return await BuildUniverseAsync(candidateSymbols, cancellationToken);
    }

    public async Task<bool> IsCacheValidAsync(CancellationToken cancellationToken = default)
    {
        if (_redis == null)
        {
            return false;
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                return false;
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                return false;
            }

            // Consider cache valid if generated within the last 24 hours
            var cacheAge = DateTime.UtcNow - universeData.GeneratedAt;
            return cacheAge < TimeSpan.FromHours(24);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking cache validity");
            return false;
        }
    }

    public IEnumerable<string> GetDefaultCandidates()
    {
        return DefaultCandidates;
    }

    /// <summary>
    /// Get candidate symbols from cache or fallback to default candidates
    /// </summary>
    private async Task<List<string>> GetCandidateSymbolsAsync(IEnumerable<string>? candidateSymbols, CancellationToken cancellationToken)
    {
        // If specific candidates provided, use them
        if (candidateSymbols != null)
        {
            return candidateSymbols.ToList();
        }

        // Try to get cached candidates from UniverseFetcherService
        if (_universeFetcherService != null)
        {
            try
            {
                _logger.LogDebug("Attempting to get cached symbols from UniverseFetcherService");
                var cachedSymbols = await _universeFetcherService.GetCachedSymbolsAsync(cancellationToken);
                if (cachedSymbols != null && cachedSymbols.Count > 0)
                {
                    _logger.LogInformation("✅ Using {Count} cached symbols from UniverseFetcherService", cachedSymbols.Count);
                    return cachedSymbols;
                }
                else
                {
                    _logger.LogWarning("⚠️ UniverseFetcherService returned null or empty cached symbols, falling back to default candidates");
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "❌ Error retrieving cached symbols from UniverseFetcherService: {ErrorMessage}, falling back to default candidates", ex.Message);
            }
        }
        else
        {
            _logger.LogDebug("UniverseFetcherService is not available (null)");
        }

        // Fallback to default candidates
        _logger.LogInformation("📋 Using {Count} default candidates as fallback (this should provide sufficient universe size)", DefaultCandidates.Length);
        return DefaultCandidates.ToList();
    }

    private async Task<(List<string> QualifiedSymbols, int ApiCalls)> ProcessBatchWithSemaphore(
        IEnumerable<string> symbols,
        Dictionary<string, int> filterBreakdown,
        SemaphoreSlim semaphore,
        CancellationToken cancellationToken)
    {
        await semaphore.WaitAsync(cancellationToken);
        try
        {
            return await ProcessBatch(symbols, filterBreakdown, cancellationToken);
        }
        finally
        {
            semaphore.Release();
        }
    }

    private async Task<(List<string> QualifiedSymbols, int ApiCalls)> ProcessBatch(
        IEnumerable<string> symbols,
        Dictionary<string, int> filterBreakdown,
        CancellationToken cancellationToken)
    {
        var qualified = new List<string>();
        var apiCalls = 0;

        foreach (var symbol in symbols)
        {
            cancellationToken.ThrowIfCancellationRequested();

            try
            {
                // Get historical data for analysis
                // Calculate end date as the most recent trading day (avoid weekends/holidays)
                var endDate = _marketCalendarService.GetLastTradingDay();
                var startDate = endDate.AddDays(-_defaultCriteria.AnalysisPeriodDays);

                // Ensure we have a valid date range (startDate should be before endDate)
                if (startDate >= endDate)
                {
                    startDate = endDate.AddDays(-Math.Max(_defaultCriteria.AnalysisPeriodDays, 30));
                }

                var response = await _marketDataService.GetStockBarsAsync(symbol, startDate, endDate);
                var bars = response.Items.ToList();
                apiCalls++;

                if (bars.Count < 10) // Need minimum data for analysis
                {
                    continue;
                }

                var currentPrice = bars.Last().Close;
                var volumes = bars.Select(b => (long)b.Volume).ToList();
                var closes = bars.Select(b => b.Close).ToList();

                // Apply filters
                if (!PassesPriceFilter(currentPrice))
                    continue;
                filterBreakdown["PassedPriceFilter"]++;

                if (!PassesVolumeFilter(volumes))
                    continue;
                filterBreakdown["PassedVolumeFilter"]++;

                if (!PassesVolatilityFilter(closes))
                    continue;
                filterBreakdown["PassedVolatilityFilter"]++;

                qualified.Add(symbol);
            }
            catch (Exception ex)
            {
                _logger.LogDebug("Error processing symbol {Symbol}: {Error}", symbol, ex.Message);
                filterBreakdown["Errors"]++;
            }
        }

        return (qualified, apiCalls);
    }

    private bool PassesPriceFilter(decimal price)
    {
        return price >= _defaultCriteria.MinPrice;
    }

    private bool PassesVolumeFilter(List<long> volumes)
    {
        if (volumes.Count == 0) return false;
        var averageVolume = volumes.Average();
        return averageVolume >= _defaultCriteria.MinAverageVolume;
    }

    private bool PassesVolatilityFilter(List<decimal> closes)
    {
        if (closes.Count < 2) return false;

        // Calculate daily returns
        var returns = new List<decimal>();
        for (int i = 1; i < closes.Count; i++)
        {
            var dailyReturn = (closes[i] - closes[i - 1]) / closes[i - 1];
            returns.Add(Math.Abs(dailyReturn));
        }

        if (returns.Count == 0) return false;

        // Calculate average absolute daily return as volatility proxy
        var averageVolatility = returns.Average();
        var volatilityPercent = averageVolatility * 100;

        return volatilityPercent >= _defaultCriteria.MinVolatilityPercent;
    }

    /// <summary>
    /// Build universe using Polygon-based symbol universe and daily candidates
    /// </summary>
    private async Task<IEnumerable<string>> BuildPolygonBasedUniverseAsync(CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogInformation("Starting Polygon-based universe building process");

            // First, try to get pre-filtered candidates from daily universe service
            if (_dailyUniverseService != null)
            {
                _logger.LogDebug("Attempting to get pre-filtered candidates from daily universe service");
                var candidates = await _dailyUniverseService.GetCurrentCandidatesAsync(cancellationToken);
                if (candidates != null && candidates.Candidates.Any())
                {
                    var candidateSymbols = candidates.Candidates.Select(c => c.Symbol).ToList();
                    _logger.LogInformation("✅ Successfully retrieved {CandidateCount} pre-filtered candidates from daily universe service", candidateSymbols.Count);
                    return candidateSymbols;
                }
                else
                {
                    _logger.LogWarning("Daily universe service returned null or empty candidates");
                }
            }
            else
            {
                _logger.LogWarning("Daily universe service is not available (null)");
            }

            _logger.LogInformation("No pre-filtered candidates available, falling back to Polygon symbol universe");

            // Fallback: Get symbols from Polygon universe and apply our own filtering
            if (_polygonSymbolService != null)
            {
                _logger.LogDebug("Attempting to get symbols from Polygon symbol service");
                var polygonSymbols = await _polygonSymbolService.GetSymbolListAsync(cancellationToken);
                var symbolTickers = polygonSymbols.Select(s => s.Ticker).ToList();

                _logger.LogInformation("✅ Successfully retrieved {SymbolCount} symbols from Polygon universe, applying filters", symbolTickers.Count);

                // Apply our traditional filtering to the Polygon symbols
                return await BuildUniverseAsync(symbolTickers, cancellationToken);
            }
            else
            {
                _logger.LogError("❌ Polygon symbol service is not available (null) - this is a critical configuration issue");
                throw new InvalidOperationException("Polygon symbol service is not available but Polygon universe is enabled");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "❌ Error building Polygon-based universe: {ErrorMessage}. Falling back to cached/default candidates", ex.Message);
            var fallbackCandidates = await GetCandidateSymbolsAsync(null, cancellationToken);
            _logger.LogWarning("Using fallback candidates: {CandidateCount} symbols", fallbackCandidates.Count);
            return await BuildUniverseAsync(fallbackCandidates, cancellationToken);
        }
    }

    /// <summary>
    /// Get cached universe with Polygon integration
    /// </summary>
    public async Task<IEnumerable<string>> GetCachedUniverseAsync(CancellationToken cancellationToken = default)
    {
        // If using Polygon universe, try to get from daily universe service first
        if (_usePolygonUniverse)
        {
            try
            {
                var candidates = await _dailyUniverseService!.GetCurrentCandidatesAsync(cancellationToken);
                if (candidates != null && candidates.Candidates.Any())
                {
                    var candidateSymbols = candidates.Candidates.Select(c => c.Symbol).ToList();
                    _logger.LogDebug("Retrieved {CandidateCount} symbols from daily universe cache", candidateSymbols.Count);
                    return candidateSymbols;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Error retrieving from daily universe service, falling back to traditional cache");
            }
        }

        // Fallback to traditional Redis cache
        if (_redis == null)
        {
            _logger.LogDebug("Redis not available, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }

        try
        {
            cancellationToken.ThrowIfCancellationRequested();
            var universeJson = await _redis.StringGetAsync(RedisUniverse.GetRedisKey());
            if (!universeJson.HasValue)
            {
                _logger.LogInformation("No cached universe found, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            var universeData = RedisUniverse.FromJson(universeJson!);
            if (universeData == null)
            {
                _logger.LogWarning("Failed to deserialize cached universe, building new universe");
                return await BuildUniverseAsync(null, cancellationToken);
            }

            _logger.LogDebug("Retrieved cached universe: {SymbolCount} symbols (generated at {GeneratedAt})",
                universeData.Symbols.Count, universeData.GeneratedAt);

            // Update universe size metric
            MetricsRegistry.CurrentUniverseSize.Set(universeData.Symbols.Count);

            return universeData.Symbols;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error retrieving cached universe, building new universe");
            return await BuildUniverseAsync(null, cancellationToken);
        }
    }



    public void Dispose()
    {
        _connectionMultiplexer?.Dispose();
    }
}
